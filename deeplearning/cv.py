# ==========================================================
# Comprehensive Cross-Validation Framework for dl_train.csv
# ==========================================================

"""
This module implements a comprehensive cross-validation framework for time-series data,
specifically designed for the dl_train.csv dataset. It provides:

Key Features:
- Time-series aware cross-validation (no data leakage)
- Comprehensive metrics reporting (AUC-ROC, AUC-PR, Log Loss, Brier Score)
- Feature importance analysis
- Confusion matrix and ROC/PR curves
- Training/validation curves
- Model parameters tracking
- Structured output to artifacts/ directory
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import time
import json
import joblib
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass, asdict

from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import (
    roc_auc_score, average_precision_score, log_loss, brier_score_loss,
    confusion_matrix, roc_curve, precision_recall_curve, classification_report
)
from sklearn.utils.class_weight import compute_sample_weight
from xgboost import XGBClassifier

# Suppress warnings
warnings.filterwarnings('ignore')

# Set random seeds for reproducibility
np.random.seed(42)

@dataclass
class CVResults:
    """Container for cross-validation results"""
    cv_scores: Dict[str, List[float]]
    cv_mean_std: Dict[str, Tuple[float, float]]
    feature_importance: pd.DataFrame
    model_params: Dict[str, Any]
    training_time: float
    inference_time: float
    best_model: Any
    predictions: Dict[str, np.ndarray]  # fold_id -> predictions


class CVEvaluator:
    """
    Comprehensive Cross-Validation Evaluator for dl_train.csv dataset
    
    Features:
    - Time-series aware cross-validation
    - Comprehensive metrics reporting
    - Feature importance analysis
    - Visualization and reporting
    """
    
    def __init__(self, data_path: str, output_dir: str = "artifacts"):
        """
        Initialize CVEvaluator
        
        Args:
            data_path: Path to dl_train.csv
            output_dir: Output directory for results
        """
        self.data_path = data_path
        self.output_dir = Path(output_dir)
        self.setup_output_dirs()
        
        # Data containers
        self.df = None
        self.features = None
        self.feature_cols = None
        self.label_cols = None
        self.metadata_cols = ['ticker', 'time', 'tvt', 'week', 'month']
        
    def setup_output_dirs(self):
        """Create output directory structure"""
        dirs = ['models', 'metrics', 'plots', 'reports']
        for dir_name in dirs:
            (self.output_dir / dir_name).mkdir(parents=True, exist_ok=True)
            
    def load_and_preprocess_data(self) -> pd.DataFrame:
        """
        Load and preprocess dl_train.csv dataset
        
        Returns:
            Preprocessed DataFrame
        """
        print("📊 Loading data from", self.data_path)
        
        # Load data in chunks to handle large files
        chunks = []
        chunk_size = 50000
        
        for chunk in pd.read_csv(self.data_path, chunksize=chunk_size):
            chunks.append(chunk)
        
        self.df = pd.concat(chunks, ignore_index=True)
        print(f"✅ Loaded {len(self.df):,} rows, {len(self.df.columns)} columns")
        
        # Convert time column
        self.df['time'] = pd.to_datetime(self.df['time'])
        
        # Identify column types
        profit_cols = [col for col in self.df.columns if col.startswith('profit_')]
        self.label_cols = [col for col in self.df.columns if col.startswith('label_')]
        
        # Feature columns = all columns except metadata, profit, and label columns
        exclude_cols = self.metadata_cols + profit_cols + self.label_cols
        self.feature_cols = [col for col in self.df.columns if col not in exclude_cols]
        
        print(f"📈 Features: {len(self.feature_cols)} columns")
        print(f"🎯 Labels: {len(self.label_cols)} columns")
        
        # Handle missing values
        print("🔧 Preprocessing data...")
        
        # Remove columns with too many missing values (>15%)
        missing_pct = self.df[self.feature_cols].isnull().mean()
        high_missing_cols = missing_pct[missing_pct > 0.15].index.tolist()
        
        if high_missing_cols:
            print(f"⚠️  Removing {len(high_missing_cols)} columns with >15% missing values")
            self.feature_cols = [col for col in self.feature_cols if col not in high_missing_cols]
        
        # Remove infinite values and remaining NaN
        self.df = self.df.replace([np.inf, -np.inf], np.nan)
        
        # Keep only rows with complete data for features and labels
        complete_cols = self.feature_cols + self.label_cols
        initial_rows = len(self.df)
        self.df = self.df.dropna(subset=complete_cols)
        final_rows = len(self.df)
        
        if initial_rows != final_rows:
            print(f"⚠️  Removed {initial_rows - final_rows:,} rows with missing values")
        
        # Sort by time for time-series consistency
        self.df = self.df.sort_values(['time', 'ticker']).reset_index(drop=True)
        
        print(f"✅ Final dataset: {len(self.df):,} rows, {len(self.feature_cols)} features")
        
        return self.df
    
    def create_time_splits(self, n_splits: int = 5, test_size: float = 0.2) -> List[Tuple[np.ndarray, np.ndarray]]:
        """
        Create time-series aware cross-validation splits
        
        Args:
            n_splits: Number of CV folds
            test_size: Fraction of data for validation in each fold
            
        Returns:
            List of (train_idx, val_idx) tuples
        """
        print(f"🔄 Creating {n_splits} time-series CV splits...")
        
        # Use existing tvt splits if available
        if 'tvt' in self.df.columns:
            train_mask = self.df['tvt'] == 'train'
            val_mask = self.df['tvt'] == 'val'
            test_mask = self.df['tvt'].isin(['test1', 'test2'])
            
            if train_mask.any() and val_mask.any():
                print("📋 Using existing tvt splits")
                train_idx = np.where(train_mask)[0]
                val_idx = np.where(val_mask)[0]
                return [(train_idx, val_idx)]
        
        # Create time-based splits
        time_sorted_idx = self.df.sort_values('time').index.values
        
        # Use TimeSeriesSplit for time-aware CV
        tscv = TimeSeriesSplit(n_splits=n_splits, test_size=int(len(time_sorted_idx) * test_size))
        
        splits = []
        for fold_idx, (train_idx, val_idx) in enumerate(tscv.split(time_sorted_idx)):
            # Map back to original indices
            train_original = time_sorted_idx[train_idx]
            val_original = time_sorted_idx[val_idx]
            splits.append((train_original, val_original))
            
            print(f"  Fold {fold_idx + 1}: Train={len(train_original):,}, Val={len(val_original):,}")
        
        return splits
    
    def compute_metrics(self, y_true: np.ndarray, y_pred_proba: np.ndarray) -> Dict[str, float]:
        """
        Compute comprehensive metrics (no threshold required)
        
        Args:
            y_true: True binary labels
            y_pred_proba: Predicted probabilities
            
        Returns:
            Dictionary of metrics
        """
        metrics = {}
        
        # Ensure we have valid predictions
        if len(np.unique(y_true)) < 2:
            print("⚠️  Warning: Only one class present in y_true")
            return {
                'roc_auc': np.nan,
                'pr_auc': np.nan,
                'log_loss': np.nan,
                'brier_score': np.nan
            }
        
        try:
            # ROC AUC
            metrics['roc_auc'] = roc_auc_score(y_true, y_pred_proba)
            
            # Precision-Recall AUC
            metrics['pr_auc'] = average_precision_score(y_true, y_pred_proba)
            
            # Log Loss
            metrics['log_loss'] = log_loss(y_true, y_pred_proba, eps=1e-15)
            
            # Brier Score
            metrics['brier_score'] = brier_score_loss(y_true, y_pred_proba)
            
        except Exception as e:
            print(f"⚠️  Error computing metrics: {e}")
            metrics = {
                'roc_auc': np.nan,
                'pr_auc': np.nan,
                'log_loss': np.nan,
                'brier_score': np.nan
            }
        
        return metrics

    def train_and_evaluate(self, label_col: str, model_params: Optional[Dict] = None,
                          n_splits: int = 5) -> CVResults:
        """
        Main training and evaluation pipeline

        Args:
            label_col: Target label column name
            model_params: XGBoost parameters (optional)
            n_splits: Number of CV folds

        Returns:
            CVResults object with comprehensive results
        """
        print(f"\n🚀 Starting cross-validation for {label_col}")

        if label_col not in self.label_cols:
            raise ValueError(f"Label column '{label_col}' not found in {self.label_cols}")

        # Default XGBoost parameters
        if model_params is None:
            model_params = {
                'objective': 'binary:logistic',
                'eval_metric': 'logloss',
                'n_estimators': 800,
                'max_depth': 6,
                'learning_rate': 0.05,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'reg_lambda': 1.0,
                'min_child_weight': 3,
                'random_state': 42,
                'n_jobs': -1,
                'verbosity': 0
            }

        # Prepare data
        X = self.df[self.feature_cols].values
        y = self.df[label_col].values

        print(f"📊 Data shape: X={X.shape}, y={y.shape}")
        print(f"🎯 Label distribution: {np.bincount(y)}")

        # Create time splits
        splits = self.create_time_splits(n_splits=n_splits)

        # Initialize results containers
        cv_scores = {
            'roc_auc': [],
            'pr_auc': [],
            'log_loss': [],
            'brier_score': []
        }

        fold_predictions = {}
        feature_importances = []
        training_times = []
        inference_times = []

        # Cross-validation loop
        print(f"\n🔄 Running {len(splits)} fold(s) cross-validation...")

        for fold_idx, (train_idx, val_idx) in enumerate(splits):
            print(f"\n📁 Fold {fold_idx + 1}/{len(splits)}")

            # Split data
            X_train, X_val = X[train_idx], X[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]

            print(f"  Train: {X_train.shape[0]:,} samples")
            print(f"  Val:   {X_val.shape[0]:,} samples")

            # Handle class imbalance
            sample_weight = compute_sample_weight('balanced', y_train)

            # Train model
            start_time = time.time()

            model = XGBClassifier(**model_params)
            model.fit(
                X_train, y_train,
                sample_weight=sample_weight,
                eval_set=[(X_val, y_val)],
                early_stopping_rounds=50,
                verbose=False
            )

            training_time = time.time() - start_time
            training_times.append(training_time)

            # Inference
            start_time = time.time()
            y_pred_proba = model.predict_proba(X_val)[:, 1]
            inference_time = time.time() - start_time
            inference_times.append(inference_time)

            # Store predictions
            fold_predictions[f'fold_{fold_idx}'] = {
                'val_idx': val_idx,
                'y_true': y_val,
                'y_pred_proba': y_pred_proba
            }

            # Compute metrics
            fold_metrics = self.compute_metrics(y_val, y_pred_proba)

            for metric_name, value in fold_metrics.items():
                cv_scores[metric_name].append(value)

            # Feature importance
            if hasattr(model, 'feature_importances_'):
                importance_df = pd.DataFrame({
                    'feature': self.feature_cols,
                    'importance': model.feature_importances_,
                    'fold': fold_idx
                })
                feature_importances.append(importance_df)

            # Print fold results
            print(f"  📈 ROC-AUC: {fold_metrics['roc_auc']:.4f}")
            print(f"  📈 PR-AUC:  {fold_metrics['pr_auc']:.4f}")
            print(f"  📈 LogLoss: {fold_metrics['log_loss']:.4f}")
            print(f"  ⏱️  Train time: {training_time:.2f}s")
            print(f"  ⏱️  Inference time: {inference_time:.4f}s")

        # Aggregate results
        cv_mean_std = {}
        for metric_name, scores in cv_scores.items():
            valid_scores = [s for s in scores if not np.isnan(s)]
            if valid_scores:
                mean_score = np.mean(valid_scores)
                std_score = np.std(valid_scores)
                cv_mean_std[metric_name] = (mean_score, std_score)
            else:
                cv_mean_std[metric_name] = (np.nan, np.nan)

        # Aggregate feature importance
        if feature_importances:
            feature_importance_df = pd.concat(feature_importances, ignore_index=True)
            feature_importance_agg = feature_importance_df.groupby('feature')['importance'].agg(['mean', 'std']).reset_index()
            feature_importance_agg = feature_importance_agg.sort_values('mean', ascending=False)
        else:
            feature_importance_agg = pd.DataFrame()

        # Train final model on all data for saving
        print(f"\n🎯 Training final model on all data...")
        final_sample_weight = compute_sample_weight('balanced', y)
        final_model = XGBClassifier(**model_params)
        final_model.fit(X, y, sample_weight=final_sample_weight)

        # Create results object
        results = CVResults(
            cv_scores=cv_scores,
            cv_mean_std=cv_mean_std,
            feature_importance=feature_importance_agg,
            model_params=model_params,
            training_time=np.mean(training_times),
            inference_time=np.mean(inference_times),
            best_model=final_model,
            predictions=fold_predictions
        )

        # Print summary
        print(f"\n📊 Cross-Validation Summary for {label_col}:")
        for metric_name, (mean_val, std_val) in cv_mean_std.items():
            if not np.isnan(mean_val):
                print(f"  {metric_name.upper()}: {mean_val:.4f} ± {std_val:.4f}")

        return results

    def plot_results(self, results: CVResults, label_col: str):
        """
        Create comprehensive visualization plots

        Args:
            results: CVResults object
            label_col: Label column name for titles
        """
        print(f"📊 Creating plots for {label_col}...")

        # Set style
        plt.style.use('default')
        sns.set_palette("husl")

        # 1. Cross-validation scores plot
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle(f'Cross-Validation Results: {label_col}', fontsize=16, fontweight='bold')

        metrics = ['roc_auc', 'pr_auc', 'log_loss', 'brier_score']
        for idx, metric in enumerate(metrics):
            ax = axes[idx // 2, idx % 2]
            scores = results.cv_scores[metric]
            valid_scores = [s for s in scores if not np.isnan(s)]

            if valid_scores:
                ax.bar(range(len(valid_scores)), valid_scores, alpha=0.7)
                ax.axhline(np.mean(valid_scores), color='red', linestyle='--',
                          label=f'Mean: {np.mean(valid_scores):.4f}')
                ax.set_title(f'{metric.upper()}')
                ax.set_xlabel('Fold')
                ax.set_ylabel('Score')
                ax.legend()
                ax.grid(True, alpha=0.3)
            else:
                ax.text(0.5, 0.5, 'No valid scores', ha='center', va='center', transform=ax.transAxes)
                ax.set_title(f'{metric.upper()} - No Data')

        plt.tight_layout()
        plt.savefig(self.output_dir / 'plots' / f'cv_scores_{label_col}.png', dpi=300, bbox_inches='tight')
        plt.close()

        # 2. Feature importance plot
        if not results.feature_importance.empty:
            plt.figure(figsize=(12, 8))
            top_features = results.feature_importance.head(20)

            plt.barh(range(len(top_features)), top_features['mean'],
                    xerr=top_features['std'], alpha=0.7)
            plt.yticks(range(len(top_features)), top_features['feature'])
            plt.xlabel('Feature Importance')
            plt.title(f'Top 20 Feature Importance: {label_col}')
            plt.gca().invert_yaxis()
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(self.output_dir / 'plots' / f'feature_importance_{label_col}.png',
                       dpi=300, bbox_inches='tight')
            plt.close()

        # 3. ROC and PR curves (using first fold data)
        if 'fold_0' in results.predictions:
            fold_data = results.predictions['fold_0']
            y_true = fold_data['y_true']
            y_pred_proba = fold_data['y_pred_proba']

            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # ROC Curve
            fpr, tpr, _ = roc_curve(y_true, y_pred_proba)
            roc_auc = roc_auc_score(y_true, y_pred_proba)

            ax1.plot(fpr, tpr, linewidth=2, label=f'ROC Curve (AUC = {roc_auc:.4f})')
            ax1.plot([0, 1], [0, 1], 'k--', alpha=0.5)
            ax1.set_xlabel('False Positive Rate')
            ax1.set_ylabel('True Positive Rate')
            ax1.set_title('ROC Curve')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # Precision-Recall Curve
            precision, recall, _ = precision_recall_curve(y_true, y_pred_proba)
            pr_auc = average_precision_score(y_true, y_pred_proba)

            ax2.plot(recall, precision, linewidth=2, label=f'PR Curve (AUC = {pr_auc:.4f})')
            ax2.axhline(y=np.mean(y_true), color='k', linestyle='--', alpha=0.5,
                       label=f'Baseline ({np.mean(y_true):.3f})')
            ax2.set_xlabel('Recall')
            ax2.set_ylabel('Precision')
            ax2.set_title('Precision-Recall Curve')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(self.output_dir / 'plots' / f'roc_pr_curves_{label_col}.png',
                       dpi=300, bbox_inches='tight')
            plt.close()

        # 4. Confusion Matrix (using threshold = 0.5)
        if 'fold_0' in results.predictions:
            fold_data = results.predictions['fold_0']
            y_true = fold_data['y_true']
            y_pred_proba = fold_data['y_pred_proba']
            y_pred = (y_pred_proba >= 0.5).astype(int)

            cm = confusion_matrix(y_true, y_pred)

            plt.figure(figsize=(8, 6))
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues',
                       xticklabels=['Negative', 'Positive'],
                       yticklabels=['Negative', 'Positive'])
            plt.title(f'Confusion Matrix: {label_col} (threshold=0.5)')
            plt.ylabel('True Label')
            plt.xlabel('Predicted Label')
            plt.tight_layout()
            plt.savefig(self.output_dir / 'plots' / f'confusion_matrix_{label_col}.png',
                       dpi=300, bbox_inches='tight')
            plt.close()

        print(f"✅ Plots saved to {self.output_dir / 'plots'}")

    def save_results(self, results: CVResults, label_col: str):
        """
        Save comprehensive results to files

        Args:
            results: CVResults object
            label_col: Label column name
        """
        print(f"💾 Saving results for {label_col}...")

        # 1. Save metrics to JSON
        metrics_dict = {
            'label_column': label_col,
            'cv_scores': results.cv_scores,
            'cv_mean_std': {k: {'mean': v[0], 'std': v[1]} for k, v in results.cv_mean_std.items()},
            'model_params': results.model_params,
            'training_time_avg': results.training_time,
            'inference_time_avg': results.inference_time,
            'n_features': len(self.feature_cols),
            'n_samples': len(self.df)
        }

        with open(self.output_dir / 'metrics' / f'cv_metrics_{label_col}.json', 'w') as f:
            json.dump(metrics_dict, f, indent=2, default=str)

        # 2. Save feature importance to CSV
        if not results.feature_importance.empty:
            results.feature_importance.to_csv(
                self.output_dir / 'metrics' / f'feature_importance_{label_col}.csv',
                index=False
            )

        # 3. Save model
        joblib.dump(results.best_model,
                   self.output_dir / 'models' / f'best_model_{label_col}.joblib')

        # 4. Save predictions
        predictions_df_list = []
        for fold_name, fold_data in results.predictions.items():
            fold_df = pd.DataFrame({
                'fold': fold_name,
                'index': fold_data['val_idx'],
                'y_true': fold_data['y_true'],
                'y_pred_proba': fold_data['y_pred_proba']
            })
            predictions_df_list.append(fold_df)

        if predictions_df_list:
            predictions_df = pd.concat(predictions_df_list, ignore_index=True)
            predictions_df.to_csv(
                self.output_dir / 'metrics' / f'cv_predictions_{label_col}.csv',
                index=False
            )

        print(f"✅ Results saved to {self.output_dir}")

    def generate_report(self, label_col: str, results: CVResults) -> str:
        """
        Generate comprehensive text report

        Args:
            label_col: Label column name
            results: CVResults object

        Returns:
            Report text
        """
        report_lines = []
        report_lines.append("=" * 80)
        report_lines.append(f"COMPREHENSIVE CROSS-VALIDATION REPORT")
        report_lines.append(f"Label: {label_col}")
        report_lines.append(f"Generated: {pd.Timestamp.now()}")
        report_lines.append("=" * 80)

        # Dataset info
        report_lines.append(f"\n📊 DATASET INFORMATION:")
        report_lines.append(f"  Total samples: {len(self.df):,}")
        report_lines.append(f"  Features: {len(self.feature_cols)}")
        report_lines.append(f"  Label distribution: {dict(zip(*np.unique(self.df[label_col], return_counts=True)))}")

        # Model parameters
        report_lines.append(f"\n🤖 MODEL PARAMETERS:")
        for param, value in results.model_params.items():
            report_lines.append(f"  {param}: {value}")

        # Cross-validation results
        report_lines.append(f"\n📈 CROSS-VALIDATION RESULTS:")
        for metric_name, (mean_val, std_val) in results.cv_mean_std.items():
            if not np.isnan(mean_val):
                report_lines.append(f"  {metric_name.upper()}: {mean_val:.4f} ± {std_val:.4f}")

        # Performance summary
        report_lines.append(f"\n⏱️  PERFORMANCE:")
        report_lines.append(f"  Average training time: {results.training_time:.2f} seconds")
        report_lines.append(f"  Average inference time: {results.inference_time:.4f} seconds")

        # Top features
        if not results.feature_importance.empty:
            report_lines.append(f"\n🎯 TOP 10 FEATURES:")
            top_features = results.feature_importance.head(10)
            for idx, row in top_features.iterrows():
                report_lines.append(f"  {row['feature']}: {row['mean']:.4f} ± {row['std']:.4f}")

        report_lines.append("\n" + "=" * 80)

        report_text = "\n".join(report_lines)

        # Save report
        with open(self.output_dir / 'reports' / f'cv_report_{label_col}.txt', 'w') as f:
            f.write(report_text)

        return report_text

    def run_comprehensive_evaluation(self, label_cols: Optional[List[str]] = None,
                                    model_params: Optional[Dict] = None,
                                    n_splits: int = 5) -> Dict[str, CVResults]:
        """
        Run comprehensive evaluation for one or more label columns

        Args:
            label_cols: List of label columns to evaluate (if None, use all available)
            model_params: XGBoost parameters
            n_splits: Number of CV folds

        Returns:
            Dictionary mapping label_col -> CVResults
        """
        # Load and preprocess data
        self.load_and_preprocess_data()

        # Determine label columns to evaluate
        if label_cols is None:
            label_cols = self.label_cols

        print(f"\n🎯 Evaluating {len(label_cols)} label(s): {label_cols}")

        all_results = {}

        for label_col in label_cols:
            print(f"\n{'='*60}")
            print(f"EVALUATING: {label_col}")
            print(f"{'='*60}")

            try:
                # Run cross-validation
                results = self.train_and_evaluate(
                    label_col=label_col,
                    model_params=model_params,
                    n_splits=n_splits
                )

                # Create plots
                self.plot_results(results, label_col)

                # Save results
                self.save_results(results, label_col)

                # Generate report
                report_text = self.generate_report(label_col, results)
                print(f"\n📋 REPORT PREVIEW:")
                print(report_text)

                all_results[label_col] = results

            except Exception as e:
                print(f"❌ Error evaluating {label_col}: {e}")
                import traceback
                traceback.print_exc()
                continue

        print(f"\n🎉 Evaluation completed for {len(all_results)} label(s)")
        print(f"📁 Results saved to: {self.output_dir}")

        return all_results


def main():
    """
    Example usage of CVEvaluator
    """
    print("🚀 Starting Comprehensive Cross-Validation Evaluation")

    # Initialize evaluator
    evaluator = CVEvaluator(
        data_path="deeplearning/dl_train.csv",
        output_dir="deeplearning/artifacts"
    )

    # Custom model parameters (optional)
    model_params = {
        'objective': 'binary:logistic',
        'eval_metric': 'logloss',
        'n_estimators': 800,
        'max_depth': 6,
        'learning_rate': 0.05,
        'subsample': 0.8,
        'colsample_bytree': 0.8,
        'reg_lambda': 1.0,
        'min_child_weight': 3,
        'random_state': 42,
        'n_jobs': -1,
        'verbosity': 0
    }

    # Run evaluation for specific labels
    target_labels = ['label_binary_1M', 'label_binary_1M_center_v1', 'label_binary_1M_center_v2']

    results = evaluator.run_comprehensive_evaluation(
        label_cols=target_labels,
        model_params=model_params,
        n_splits=5
    )

    # Print summary
    print(f"\n📊 FINAL SUMMARY:")
    print(f"{'='*60}")

    for label_col, result in results.items():
        print(f"\n🎯 {label_col}:")
        for metric_name, (mean_val, std_val) in result.cv_mean_std.items():
            if not np.isnan(mean_val):
                print(f"  {metric_name.upper()}: {mean_val:.4f} ± {std_val:.4f}")

    print(f"\n✅ All results saved to: deeplearning/artifacts/")
    return results


if __name__ == "__main__":
    # Run the evaluation
    results = main()
